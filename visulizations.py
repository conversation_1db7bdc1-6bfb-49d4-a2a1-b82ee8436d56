import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from matplotlib.backends.backend_pdf import PdfPages
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set style for better looking plots
plt.style.use('default')
sns.set_palette("husl")

def load_and_preprocess_data(csv_file_path):
    """
    Load and preprocess the SCADA vs API comparison data
    Assumes CSV columns: Date, Time, Inverter, SCADA_Value, API_Value, Difference, Pct_Difference, Match_Status
    """
    # Read CSV file
    df = pd.read_csv(csv_file_path)
    
    # Combine date and time columns if they're separate
    if 'Date' in df.columns and 'Time' in df.columns:
        df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
    elif 'DateTime' in df.columns:
        df['DateTime'] = pd.to_datetime(df['DateTime'])
    
    # Extract hour for hourly analysis
    df['Hour'] = df['DateTime'].dt.hour
    
    # Create bins for percentage differences
    df['Pct_Diff_Bin'] = pd.cut(df['Pct_Difference'], 
                                bins=[-np.inf, 0, 5, 10, 25, 50, 100, np.inf],
                                labels=['≤0%', '0-5%', '5-10%', '10-25%', '25-50%', '50-100%', '>100%'])
    
    # Create bins for absolute differences
    df['Abs_Diff_Bin'] = pd.cut(np.abs(df['Difference']), 
                                bins=[0, 0.1, 0.5, 1, 5, 10, np.inf],
                                labels=['0-0.1', '0.1-0.5', '0.5-1', '1-5', '5-10', '>10'])
    
    return df

def create_visualizations(df, output_pdf_path):
    """
    Create comprehensive visualizations and save to PDF
    """
    
    with PdfPages(output_pdf_path) as pdf:
        
        # Page 1: Overview Dashboard
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('SCADA vs API Comparison - Overview Dashboard', fontsize=16, fontweight='bold')
        
        # Match Status Distribution (Pie Chart)
        match_counts = df['Match_Status'].value_counts()
        colors = ['#ff9999', '#66b3ff', '#99ff99']
        ax1.pie(match_counts.values, labels=match_counts.index, autopct='%1.1f%%', 
                colors=colors, startangle=90)
        ax1.set_title('Match Status Distribution')
        
        # Match Rate by Inverter (Bar Chart)
        inverter_stats = df.groupby('Inverter').agg({
            'Match_Status': lambda x: (x == 'Match').sum() / len(x) * 100
        }).round(1)
        inverter_stats.columns = ['Match_Rate']
        ax2.bar(inverter_stats.index, inverter_stats['Match_Rate'], color='skyblue')
        ax2.set_title('Match Rate by Inverter (%)')
        ax2.set_xlabel('Inverter')
        ax2.set_ylabel('Match Rate (%)')
        ax2.tick_params(axis='x', rotation=45)
        
        # Hourly Match Rate
        hourly_stats = df.groupby('Hour').agg({
            'Match_Status': lambda x: (x == 'Match').sum() / len(x) * 100
        }).round(1)
        ax3.plot(hourly_stats.index, hourly_stats['Match_Status'], marker='o', linewidth=2)
        ax3.set_title('Match Rate by Hour of Day')
        ax3.set_xlabel('Hour')
        ax3.set_ylabel('Match Rate (%)')
        ax3.grid(True, alpha=0.3)
        
        # Percentage Difference Distribution
        pct_diff_counts = df['Pct_Diff_Bin'].value_counts()
        ax4.bar(range(len(pct_diff_counts)), pct_diff_counts.values, 
                color='lightcoral', alpha=0.7)
        ax4.set_title('Distribution of Percentage Differences')
        ax4.set_xlabel('Percentage Difference Bins')
        ax4.set_ylabel('Count')
        ax4.set_xticks(range(len(pct_diff_counts)))
        ax4.set_xticklabels(pct_diff_counts.index, rotation=45)
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # Page 2: Detailed Inverter Analysis
        fig, axes = plt.subplots(3, 3, figsize=(18, 15))
        fig.suptitle('Individual Inverter Performance Analysis', fontsize=16, fontweight='bold')
        
        inverters = sorted(df['Inverter'].unique())
        for i, inverter in enumerate(inverters):
            if i < 9:  # Assuming 9 inverters as per your data
                row, col = i // 3, i % 3
                ax = axes[row, col]
                
                inv_data = df[df['Inverter'] == inverter]
                match_counts = inv_data['Match_Status'].value_counts()
                
                ax.pie(match_counts.values, labels=match_counts.index, autopct='%1.1f%%',
                       startangle=90, colors=['#ff9999', '#66b3ff', '#99ff99'])
                ax.set_title(f'{inverter} - Match Distribution')
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # Page 3: Time Series Analysis
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
        fig.suptitle('Time Series Analysis', fontsize=16, fontweight='bold')
        
        # Hourly mismatch pattern
        hourly_mismatch = df[df['Match_Status'] == 'Mismatch'].groupby('Hour').size()
        ax1.bar(hourly_mismatch.index, hourly_mismatch.values, color='salmon', alpha=0.7)
        ax1.set_title('Mismatch Count by Hour')
        ax1.set_xlabel('Hour of Day')
        ax1.set_ylabel('Number of Mismatches')
        ax1.grid(True, alpha=0.3)
        
        # Average percentage difference by hour
        hourly_avg_diff = df.groupby('Hour')['Pct_Difference'].mean()
        ax2.plot(hourly_avg_diff.index, hourly_avg_diff.values, marker='o', 
                linewidth=2, color='red')
        ax2.set_title('Average Percentage Difference by Hour')
        ax2.set_xlabel('Hour of Day')
        ax2.set_ylabel('Average Percentage Difference (%)')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # Page 4: Binned Analysis
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Binned Analysis - Data Distribution Patterns', fontsize=16, fontweight='bold')
        
        # Percentage difference bins
        pct_diff_counts = df['Pct_Diff_Bin'].value_counts().sort_index()
        ax1.bar(range(len(pct_diff_counts)), pct_diff_counts.values, color='lightblue')
        ax1.set_title('Distribution by Percentage Difference Bins')
        ax1.set_xlabel('Percentage Difference Bins')
        ax1.set_ylabel('Count')
        ax1.set_xticks(range(len(pct_diff_counts)))
        ax1.set_xticklabels(pct_diff_counts.index, rotation=45)
        
        # Absolute difference bins
        abs_diff_counts = df['Abs_Diff_Bin'].value_counts().sort_index()
        ax2.bar(range(len(abs_diff_counts)), abs_diff_counts.values, color='lightgreen')
        ax2.set_title('Distribution by Absolute Difference Bins')
        ax2.set_xlabel('Absolute Difference Bins')
        ax2.set_ylabel('Count')
        ax2.set_xticks(range(len(abs_diff_counts)))
        ax2.set_xticklabels(abs_diff_counts.index, rotation=45)
        
        # Heatmap: Inverter vs Hour (Match Rate)
        pivot_data = df.pivot_table(values='Match_Status', index='Inverter', columns='Hour', 
                                   aggfunc=lambda x: (x == 'Match').sum() / len(x) * 100)
        sns.heatmap(pivot_data, annot=True, fmt='.1f', cmap='RdYlGn', ax=ax3)
        ax3.set_title('Match Rate Heatmap (Inverter vs Hour)')
        
        # Box plot: Percentage difference by inverter
        mismatch_data = df[df['Match_Status'] == 'Mismatch']
        inverter_order = mismatch_data.groupby('Inverter')['Pct_Difference'].median().sort_values().index
        sns.boxplot(data=mismatch_data, x='Inverter', y='Pct_Difference', 
                   order=inverter_order, ax=ax4)
        ax4.set_title('Percentage Difference Distribution by Inverter')
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # Page 5: Advanced Analytics
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Advanced Analytics & Insights', fontsize=16, fontweight='bold')
        
        # SCADA vs API scatter plot (for mismatches only)
        mismatch_sample = mismatch_data.sample(n=min(1000, len(mismatch_data)))  # Sample for readability
        ax1.scatter(mismatch_sample['SCADA_Value'], mismatch_sample['API_Value'], 
                   alpha=0.6, c='red')
        ax1.plot([0, mismatch_sample[['SCADA_Value', 'API_Value']].max().max()], 
                [0, mismatch_sample[['SCADA_Value', 'API_Value']].max().max()], 
                'k--', alpha=0.8)
        ax1.set_xlabel('SCADA Value')
        ax1.set_ylabel('API Value')
        ax1.set_title('SCADA vs API Values (Mismatches)')
        
        # API vs SCADA dominance
        api_higher = len(df[(df['API_Value'] > df['SCADA_Value']) & (df['Match_Status'] == 'Mismatch')])
        scada_higher = len(df[(df['SCADA_Value'] > df['API_Value']) & (df['Match_Status'] == 'Mismatch')])
        
        ax2.pie([api_higher, scada_higher], labels=['API Higher', 'SCADA Higher'], 
               autopct='%1.1f%%', colors=['#ff6b6b', '#4ecdc4'])
        ax2.set_title('Mismatch Patterns: Which Source Reports Higher?')
        
        # Cumulative percentage differences
        sorted_pct_diff = np.sort(df[df['Match_Status'] == 'Mismatch']['Pct_Difference'].values)
        cumulative_pct = np.arange(1, len(sorted_pct_diff) + 1) / len(sorted_pct_diff) * 100
        ax3.plot(sorted_pct_diff, cumulative_pct, linewidth=2, color='purple')
        ax3.set_xlabel('Percentage Difference')
        ax3.set_ylabel('Cumulative Percentage (%)')
        ax3.set_title('Cumulative Distribution of Percentage Differences')
        ax3.grid(True, alpha=0.3)
        
        # Weekly pattern (if data spans multiple days)
        if len(df['DateTime'].dt.date.unique()) > 1:
            df['DayOfWeek'] = df['DateTime'].dt.day_name()
            weekly_match_rate = df.groupby('DayOfWeek').agg({
                'Match_Status': lambda x: (x == 'Match').sum() / len(x) * 100
            })
            ax4.bar(weekly_match_rate.index, weekly_match_rate['Match_Status'], color='orange')
            ax4.set_title('Match Rate by Day of Week')
            ax4.tick_params(axis='x', rotation=45)
        else:
            # Show distribution of differences instead
            ax4.hist(df[df['Match_Status'] == 'Mismatch']['Difference'], 
                    bins=30, alpha=0.7, color='orange')
            ax4.set_title('Distribution of Differences (Mismatches)')
            ax4.set_xlabel('Difference (SCADA - API)')
            ax4.set_ylabel('Frequency')
        
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()
        
        # Page 6: Summary Statistics Table
        fig, ax = plt.subplots(figsize=(16, 10))
        ax.axis('tight')
        ax.axis('off')
        
        # Create summary statistics
        summary_stats = []
        
        # Overall statistics
        total_records = len(df)
        match_rate = (df['Match_Status'] == 'Match').sum() / total_records * 100
        missing_data_rate = (df['Match_Status'] == 'Missing Data').sum() / total_records * 100
        
        summary_stats.append(['Overall Statistics', '', ''])
        summary_stats.append(['Total Records', f'{total_records:,}', ''])
        summary_stats.append(['Match Rate', f'{match_rate:.1f}%', ''])
        summary_stats.append(['Missing Data Rate', f'{missing_data_rate:.1f}%', ''])
        summary_stats.append(['', '', ''])
        
        # Per inverter statistics
        summary_stats.append(['Inverter Performance', 'Match Rate', 'Avg % Diff'])
        for inverter in sorted(df['Inverter'].unique()):
            inv_data = df[df['Inverter'] == inverter]
            inv_match_rate = (inv_data['Match_Status'] == 'Match').sum() / len(inv_data) * 100
            inv_avg_diff = inv_data[inv_data['Match_Status'] == 'Mismatch']['Pct_Difference'].mean()
            summary_stats.append([inverter, f'{inv_match_rate:.1f}%', f'{inv_avg_diff:.1f}%'])
        
        # Create table
        table = ax.table(cellText=summary_stats, 
                        colLabels=['Metric', 'Value', 'Additional'],
                        cellLoc='center',
                        loc='center',
                        colWidths=[0.4, 0.3, 0.3])
        
        table.auto_set_font_size(False)
        table.set_fontsize(12)
        table.scale(1.2, 2)
        
        # Style the table
        for i in range(len(summary_stats) + 1):
            for j in range(3):
                cell = table[(i, j)]
                if i == 0:  # Header
                    cell.set_facecolor('#4CAF50')
                    cell.set_text_props(weight='bold', color='white')
                elif summary_stats[i-1][0] in ['Overall Statistics', 'Inverter Performance']:
                    cell.set_facecolor('#E8F5E8')
                    cell.set_text_props(weight='bold')
        
        ax.set_title('Summary Statistics Report', fontsize=16, fontweight='bold', pad=20)
        
        pdf.savefig(fig, bbox_inches='tight')
        plt.close()

def main():
    """
    Main function to run the analysis
    """
    # File paths - UPDATE THESE PATHS
    csv_file_path = r'validation_results\comparison_results.csv'  # Replace with your CSV file path
    output_pdf_path = 'SCADA_API_Analysis_Report_KPLS.pdf'
    
    try:
        # Load and preprocess data
        print("Loading and preprocessing data...")
        df = load_and_preprocess_data(csv_file_path)
        print(f"Data loaded successfully. Shape: {df.shape}")
        
        # Create visualizations
        print("Creating visualizations...")
        create_visualizations(df, output_pdf_path)
        print(f"Analysis complete! Report saved as: {output_pdf_path}")
        
        # Print basic statistics
        print("\nBasic Statistics:")
        print(f"Total records: {len(df):,}")
        print(f"Date range: {df['DateTime'].min()} to {df['DateTime'].max()}")
        print(f"Match rate: {(df['Match_Status'] == 'Match').sum() / len(df) * 100:.1f}%")
        print(f"Unique inverters: {df['Inverter'].nunique()}")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Please check your CSV file path and format.")

if __name__ == "__main__":
    main()

# Additional utility functions for custom analysis

def analyze_specific_inverter(df, inverter_id):
    """
    Detailed analysis for a specific inverter
    """
    inv_data = df[df['Inverter'] == inverter_id]
    
    print(f"\nDetailed Analysis for {inverter_id}:")
    print(f"Total records: {len(inv_data)}")
    print(f"Match rate: {(inv_data['Match_Status'] == 'Match').sum() / len(inv_data) * 100:.1f}%")
    print(f"Average percentage difference: {inv_data['Pct_Difference'].mean():.2f}%")
    print(f"Max percentage difference: {inv_data['Pct_Difference'].max():.2f}%")
    
    return inv_data

def analyze_time_period(df, start_hour, end_hour):
    """
    Analyze data for a specific time period
    """
    period_data = df[(df['Hour'] >= start_hour) & (df['Hour'] <= end_hour)]
    
    print(f"\nAnalysis for hours {start_hour}-{end_hour}:")
    print(f"Total records: {len(period_data)}")
    print(f"Match rate: {(period_data['Match_Status'] == 'Match').sum() / len(period_data) * 100:.1f}%")
    
    return period_data
